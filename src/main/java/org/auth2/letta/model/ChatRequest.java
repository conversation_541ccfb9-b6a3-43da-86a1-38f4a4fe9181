package org.auth2.letta.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;

/**
 * 聊天请求模型
 */
public class ChatRequest {
    
    @JsonProperty("messages")
    private List<Message> messages;
    
    @JsonProperty("stream_steps")
    private boolean streamSteps;
    
    @JsonProperty("stream_tokens")
    private boolean streamTokens;
    
    public ChatRequest() {}
    
    public ChatRequest(List<Message> messages, boolean streamSteps, boolean streamTokens) {
        this.messages = messages;
        this.streamSteps = streamSteps;
        this.streamTokens = streamTokens;
    }
    
    public List<Message> getMessages() {
        return messages;
    }
    
    public void setMessages(List<Message> messages) {
        this.messages = messages;
    }
    
    public boolean isStreamSteps() {
        return streamSteps;
    }
    
    public void setStreamSteps(boolean streamSteps) {
        this.streamSteps = streamSteps;
    }
    
    public boolean isStreamTokens() {
        return streamTokens;
    }
    
    public void setStreamTokens(boolean streamTokens) {
        this.streamTokens = streamTokens;
    }
    
    @Override
    public String toString() {
        return "ChatRequest{" +
                "messages=" + messages +
                ", streamSteps=" + streamSteps +
                ", streamTokens=" + streamTokens +
                '}';
    }
}
