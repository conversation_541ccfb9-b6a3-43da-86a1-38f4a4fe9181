package org.auth2.letta.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.auth2.letta.model.ChatRequest;
import org.auth2.letta.model.Message;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;

import java.util.Arrays;

/**
 * Letta Agent 服务
 */
@Service
public class LettaAgentService {
    
    private final WebClient webClient;
    private final ObjectMapper objectMapper;
    
    @Value("${letta.agent.base-url:http://localhost:8283}")
    private String baseUrl;
    
    @Value("${letta.agent.id:agent-491a30d3-f49a-4b24-bf07-9d78064ce7cd}")
    private String agentId;
    
    public LettaAgentService() {
        this.webClient = WebClient.builder().build();
        this.objectMapper = new ObjectMapper();
    }
    
    /**
     * 发送消息到Letta Agent并获取流式响应
     * @param content 消息内容
     * @return 流式响应
     */
    public Flux<String> sendMessage(String content) {
        // 构建请求
        Message message = new Message("user", content);
        ChatRequest request = new ChatRequest(Arrays.asList(message), true, true);
        
        String url = String.format("%s/v1/agents/%s/messages/stream", baseUrl, agentId);
        
        return webClient.post()
                .uri(url)
                .header("Content-Type", "application/json")
                .header("Accept", "text/event-stream")
                .bodyValue(request)
                .retrieve()
                .bodyToFlux(String.class)
                .onErrorResume(throwable -> {
                    System.err.println("Error occurred: " + throwable.getMessage());
                    return Flux.just("Error: " + throwable.getMessage());
                });
    }
    
    /**
     * 发送消息到Letta Agent并获取完整响应
     * @param content 消息内容
     * @return 完整响应字符串
     */
    public String sendMessageSync(String content) {
        StringBuilder response = new StringBuilder();
        
        sendMessage(content)
                .doOnNext(chunk -> response.append(chunk))
                .doOnError(error -> response.append("Error: ").append(error.getMessage()))
                .blockLast(); // 等待流完成
        
        return response.toString();
    }
}
