//package org.auth2.letta.simple;
//
//import com.sun.net.httpserver.HttpExchange;
//import com.sun.net.httpserver.HttpHandler;
//import com.sun.net.httpserver.HttpServer;
//
//import java.io.*;
//import java.net.HttpURLConnection;
//import java.net.InetSocketAddress;
//import java.net.URL;
//import java.nio.charset.StandardCharsets;
//import java.util.concurrent.Executors;
//
///**
// * Simple Letta MCP Server implementation without Spring Boot
// */
//public class SimpleLettaMcpServer {
//
//    private static final String LETTA_BASE_URL = "http://localhost:8283";
//    private static final String AGENT_ID = "agent-491a30d3-f49a-4b24-bf07-9d78064ce7cd";
//    private static final int SERVER_PORT = 8080;
//
//    public static void main(String[] args) throws IOException {
//        HttpServer server = HttpServer.create(new InetSocketAddress(SERVER_PORT), 0);
//
//        // 健康检查
//        server.createContext("/test/health", new HealthHandler());
//
//        // MCP初始化
//        server.createContext("/mcp/initialize", new InitializeHandler());
//
//        // MCP工具列表
//        server.createContext("/mcp/tools/list", new ToolsListHandler());
//
//        // MCP工具调用
//        server.createContext("/mcp/tools/call", new ToolsCallHandler());
//
//        // 测试聊天
//        server.createContext("/test/chat", new ChatHandler());
//
//        server.setExecutor(Executors.newFixedThreadPool(10));
//        server.start();
//
//        System.out.println("Letta MCP Server started on port " + SERVER_PORT);
//        System.out.println("Health check: http://localhost:" + SERVER_PORT + "/test/health");
//        System.out.println("Test chat: http://localhost:" + SERVER_PORT + "/test/chat");
//    }
//
//    static class HealthHandler implements HttpHandler {
//        @Override
//        public void handle(HttpExchange exchange) throws IOException {
//            if ("GET".equals(exchange.getRequestMethod())) {
//                String response = "{\"status\":\"UP\",\"service\":\"Letta MCP Server\"}";
//                sendResponse(exchange, 200, response, "application/json");
//            } else {
//                sendResponse(exchange, 405, "Method Not Allowed", "text/plain");
//            }
//        }
//    }
//
//    static class InitializeHandler implements HttpHandler {
//        @Override
//        public void handle(HttpExchange exchange) throws IOException {
//            if ("POST".equals(exchange.getRequestMethod())) {
//                String response = "{\n" +
//                        "  \"protocolVersion\": \"2024-11-05\",\n" +
//                        "  \"capabilities\": {\n" +
//                        "    \"tools\": {\n" +
//                        "      \"listChanged\": false\n" +
//                        "    }\n" +
//                        "  },\n" +
//                        "  \"serverInfo\": {\n" +
//                        "    \"name\": \"letta-mcp-server\",\n" +
//                        "    \"version\": \"1.0.0\"\n" +
//                        "  }\n" +
//                        "}";
//                sendResponse(exchange, 200, response, "application/json");
//            } else {
//                sendResponse(exchange, 405, "Method Not Allowed", "text/plain");
//            }
//        }
//    }
//
//    static class ToolsListHandler implements HttpHandler {
//        @Override
//        public void handle(HttpExchange exchange) throws IOException {
//            if ("POST".equals(exchange.getRequestMethod())) {
//                String response = "{\n" +
//                        "  \"tools\": [\n" +
//                        "    {\n" +
//                        "      \"name\": \"letta_chat\",\n" +
//                        "      \"description\": \"Send a message to Letta Agent and get response\",\n" +
//                        "      \"inputSchema\": {\n" +
//                        "        \"type\": \"object\",\n" +
//                        "        \"properties\": {\n" +
//                        "          \"message\": {\n" +
//                        "            \"type\": \"string\",\n" +
//                        "            \"description\": \"The message to send to Letta Agent\"\n" +
//                        "          }\n" +
//                        "        },\n" +
//                        "        \"required\": [\"message\"]\n" +
//                        "      }\n" +
//                        "    }\n" +
//                        "  ]\n" +
//                        "}";
//                sendResponse(exchange, 200, response, "application/json");
//            } else {
//                sendResponse(exchange, 405, "Method Not Allowed", "text/plain");
//            }
//        }
//    }
//
//    static class ToolsCallHandler implements HttpHandler {
//        @Override
//        public void handle(HttpExchange exchange) throws IOException {
//            if ("POST".equals(exchange.getRequestMethod())) {
//                String requestBody = readRequestBody(exchange);
//                System.out.println("Tool call request: " + requestBody);
//
//                // 简单解析JSON (在实际应用中应该使用JSON库)
//                if (requestBody.contains("\"letta_chat\"")) {
//                    // 提取消息内容
//                    String message = extractMessage(requestBody);
//                    if (message != null) {
//                        String lettaResponse = callLettaAgent(message);
//                        String response = "{\n" +
//                                "  \"content\": [\n" +
//                                "    {\n" +
//                                "      \"type\": \"text\",\n" +
//                                "      \"text\": \"" + escapeJson(lettaResponse) + "\"\n" +
//                                "    }\n" +
//                                "  ]\n" +
//                                "}";
//                        sendResponse(exchange, 200, response, "application/json");
//                    } else {
//                        sendErrorResponse(exchange, "Failed to extract message from request");
//                    }
//                } else {
//                    sendErrorResponse(exchange, "Unknown tool");
//                }
//            } else {
//                sendResponse(exchange, 405, "Method Not Allowed", "text/plain");
//            }
//        }
//    }
//
//    static class ChatHandler implements HttpHandler {
//        @Override
//        public void handle(HttpExchange exchange) throws IOException {
//            if ("POST".equals(exchange.getRequestMethod())) {
//                String requestBody = readRequestBody(exchange);
//                String message = extractMessage(requestBody);
//
//                if (message != null && !message.trim().isEmpty()) {
//                    String lettaResponse = callLettaAgent(message);
//                    String response = "{\"response\":\"" + escapeJson(lettaResponse) + "\"}";
//                    sendResponse(exchange, 200, response, "application/json");
//                } else {
//                    sendResponse(exchange, 400, "{\"error\":\"Message cannot be empty\"}", "application/json");
//                }
//            } else {
//                sendResponse(exchange, 405, "Method Not Allowed", "text/plain");
//            }
//        }
//    }
//
//    private static String callLettaAgent(String message) {
//        try {
//            String url = LETTA_BASE_URL + "/v1/agents/" + AGENT_ID + "/messages/stream";
//            String jsonPayload = "{\n" +
//                    "  \"messages\": [\n" +
//                    "    {\n" +
//                    "      \"role\": \"user\",\n" +
//                    "      \"content\": \"" + escapeJson(message) + "\"\n" +
//                    "    }\n" +
//                    "  ],\n" +
//                    "  \"stream_steps\": true,\n" +
//                    "  \"stream_tokens\": true\n" +
//                    "}";
//
//            URL obj = new URL(url);
//            HttpURLConnection con = (HttpURLConnection) obj.openConnection();
//            con.setRequestMethod("POST");
//            con.setRequestProperty("Content-Type", "application/json");
//            con.setRequestProperty("Accept", "text/event-stream");
//            con.setDoOutput(true);
//
//            try (OutputStream os = con.getOutputStream()) {
//                byte[] input = jsonPayload.getBytes(StandardCharsets.UTF_8);
//                os.write(input, 0, input.length);
//            }
//
//            int responseCode = con.getResponseCode();
//            if (responseCode == HttpURLConnection.HTTP_OK) {
//                try (BufferedReader in = new BufferedReader(new InputStreamReader(con.getInputStream()))) {
//                    StringBuilder response = new StringBuilder();
//                    String inputLine;
//                    while ((inputLine = in.readLine()) != null) {
//                        response.append(inputLine).append("\n");
//                    }
//                    return response.toString();
//                }
//            } else {
//                return "Error: HTTP " + responseCode;
//            }
//        } catch (Exception e) {
//            return "Error: " + e.getMessage();
//        }
//    }
//
//    private static String extractMessage(String json) {
//        // 简单的JSON解析，在实际应用中应该使用JSON库
//        int messageIndex = json.indexOf("\"message\"");
//        if (messageIndex != -1) {
//            int colonIndex = json.indexOf(":", messageIndex);
//            if (colonIndex != -1) {
//                int startQuote = json.indexOf("\"", colonIndex);
//                if (startQuote != -1) {
//                    int endQuote = json.indexOf("\"", startQuote + 1);
//                    if (endQuote != -1) {
//                        return json.substring(startQuote + 1, endQuote);
//                    }
//                }
//            }
//        }
//        return null;
//    }
//
//    private static String escapeJson(String text) {
//        if (text == null) return "";
//        return text.replace("\\", "\\\\")
//                   .replace("\"", "\\\"")
//                   .replace("\n", "\\n")
//                   .replace("\r", "\\r")
//                   .replace("\t", "\\t");
//    }
//
//    private static String readRequestBody(HttpExchange exchange) throws IOException {
//        try (BufferedReader reader = new BufferedReader(new InputStreamReader(exchange.getRequestBody()))) {
//            StringBuilder body = new StringBuilder();
//            String line;
//            while ((line = reader.readLine()) != null) {
//                body.append(line);
//            }
//            return body.toString();
//        }
//    }
//
//    private static void sendResponse(HttpExchange exchange, int statusCode, String response, String contentType) throws IOException {
//        exchange.getResponseHeaders().set("Content-Type", contentType);
//        exchange.getResponseHeaders().set("Access-Control-Allow-Origin", "*");
//        exchange.getResponseHeaders().set("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
//        exchange.getResponseHeaders().set("Access-Control-Allow-Headers", "Content-Type");
//
//        byte[] responseBytes = response.getBytes(StandardCharsets.UTF_8);
//        exchange.sendResponseHeaders(statusCode, responseBytes.length);
//        try (OutputStream os = exchange.getResponseBody()) {
//            os.write(responseBytes);
//        }
//    }
//
//    private static void sendErrorResponse(HttpExchange exchange, String error) throws IOException {
//        String response = "{\n" +
//                "  \"content\": [\n" +
//                "    {\n" +
//                "      \"type\": \"text\",\n" +
//                "      \"text\": \"Error: " + escapeJson(error) + "\"\n" +
//                "    }\n" +
//                "  ]\n" +
//                "}";
//        sendResponse(exchange, 500, response, "application/json");
//    }
//}
