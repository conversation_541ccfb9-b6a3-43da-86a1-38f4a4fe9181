package org.auth2.letta.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.auth2.letta.service.LettaAgentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

import java.util.HashMap;
import java.util.Map;

/**
 * MCP Server 控制器
 */
@RestController
@RequestMapping("/mcp")
@CrossOrigin(origins = "*")
public class McpController {
    
    @Autowired
    private LettaAgentService lettaAgentService;
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * MCP Server 初始化
     */
    @PostMapping("/initialize")
    public ResponseEntity<Map<String, Object>> initialize(@RequestBody Map<String, Object> request) {
        Map<String, Object> response = new HashMap<>();
        response.put("protocolVersion", "2024-11-05");
        response.put("capabilities", createCapabilities());
        response.put("serverInfo", createServerInfo());
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 列出可用的工具
     */
    @PostMapping("/tools/list")
    public ResponseEntity<Map<String, Object>> listTools(@RequestBody Map<String, Object> request) {
        Map<String, Object> response = new HashMap<>();
        response.put("tools", createTools());
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 调用工具
     */
    @PostMapping("/tools/call")
    public ResponseEntity<Map<String, Object>> callTool(@RequestBody Map<String, Object> request) {
        try {
            JsonNode requestNode = objectMapper.valueToTree(request);
            String toolName = requestNode.get("params").get("name").asText();
            JsonNode arguments = requestNode.get("params").get("arguments");
            
            if ("letta_chat".equals(toolName)) {
                String message = arguments.get("message").asText();
                String response = lettaAgentService.sendMessageSync(message);
                
                Map<String, Object> result = new HashMap<>();
                result.put("content", createToolResult(response));
                return ResponseEntity.ok(result);
            }
            
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("content", createToolError("Unknown tool: " + toolName));
            return ResponseEntity.badRequest().body(errorResult);
            
        } catch (Exception e) {
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("content", createToolError("Error: " + e.getMessage()));
            return ResponseEntity.internalServerError().body(errorResult);
        }
    }
    
    /**
     * 流式聊天接口
     */
    @PostMapping(value = "/chat/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<String> chatStream(@RequestBody Map<String, String> request) {
        String message = request.get("message");
        if (message == null || message.trim().isEmpty()) {
            return Flux.just("data: Error: Message cannot be empty\n\n");
        }
        
        return lettaAgentService.sendMessage(message)
                .map(chunk -> "data: " + chunk + "\n\n")
                .onErrorReturn("data: Error occurred during streaming\n\n");
    }
    
    private Map<String, Object> createCapabilities() {
        Map<String, Object> capabilities = new HashMap<>();
        Map<String, Object> tools = new HashMap<>();
        tools.put("listChanged", false);
        capabilities.put("tools", tools);
        return capabilities;
    }
    
    private Map<String, Object> createServerInfo() {
        Map<String, Object> serverInfo = new HashMap<>();
        serverInfo.put("name", "letta-mcp-server");
        serverInfo.put("version", "1.0.0");
        return serverInfo;
    }
    
    private Object[] createTools() {
        Map<String, Object> tool = new HashMap<>();
        tool.put("name", "letta_chat");
        tool.put("description", "Send a message to Letta Agent and get streaming response");
        
        Map<String, Object> inputSchema = new HashMap<>();
        inputSchema.put("type", "object");
        
        Map<String, Object> properties = new HashMap<>();
        Map<String, Object> messageProperty = new HashMap<>();
        messageProperty.put("type", "string");
        messageProperty.put("description", "The message to send to Letta Agent");
        properties.put("message", messageProperty);
        
        inputSchema.put("properties", properties);
        inputSchema.put("required", new String[]{"message"});
        
        tool.put("inputSchema", inputSchema);
        
        return new Object[]{tool};
    }
    
    private Object[] createToolResult(String content) {
        Map<String, Object> result = new HashMap<>();
        result.put("type", "text");
        result.put("text", content);
        return new Object[]{result};
    }
    
    private Object[] createToolError(String error) {
        Map<String, Object> result = new HashMap<>();
        result.put("type", "text");
        result.put("text", error);
        return new Object[]{result};
    }
}
