package org.auth2.letta.controller;

import org.auth2.letta.service.LettaAgentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

import java.util.HashMap;
import java.util.Map;

/**
 * 测试控制器
 */
@RestController
@RequestMapping("/test")
@CrossOrigin(origins = "*")
public class TestController {
    
    @Autowired
    private LettaAgentService lettaAgentService;
    
    /**
     * 健康检查
     */
    @GetMapping("/health")
    public Map<String, String> health() {
        Map<String, String> response = new HashMap<>();
        response.put("status", "UP");
        response.put("service", "Letta MCP Server");
        return response;
    }
    
    /**
     * 测试同步聊天
     */
    @PostMapping("/chat")
    public Map<String, String> testChat(@RequestBody Map<String, String> request) {
        String message = request.get("message");
        if (message == null || message.trim().isEmpty()) {
            Map<String, String> error = new HashMap<>();
            error.put("error", "Message cannot be empty");
            return error;
        }
        
        try {
            String response = lettaAgentService.sendMessageSync(message);
            Map<String, String> result = new HashMap<>();
            result.put("response", response);
            return result;
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("error", "Failed to get response: " + e.getMessage());
            return error;
        }
    }
    
    /**
     * 测试流式聊天
     */
    @PostMapping(value = "/chat/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<String> testChatStream(@RequestBody Map<String, String> request) {
        String message = request.get("message");
        if (message == null || message.trim().isEmpty()) {
            return Flux.just("data: Error: Message cannot be empty\n\n");
        }
        
        return lettaAgentService.sendMessage(message)
                .map(chunk -> "data: " + chunk + "\n\n")
                .onErrorReturn("data: Error occurred during streaming\n\n");
    }
}
