@echo off
echo Compiling and running Letta MCP Server without Maven...
echo.

set JAVA_HOME=D:\soft\jdk8
set CLASSPATH=.

echo Creating lib directory...
if not exist lib mkdir lib

echo Downloading required JAR files...
echo Note: You need to manually download the following JAR files to the lib directory:
echo - spring-boot-starter-web-2.6.13.jar
echo - spring-boot-starter-webflux-2.6.13.jar  
echo - jackson-databind-2.13.4.jar
echo - jackson-core-2.13.4.jar
echo - httpclient-4.5.13.jar
echo.
echo Or use <PERSON><PERSON> to compile the project properly.
echo.

pause
