#!/bin/bash

echo "Starting Letta MCP Server..."
echo

# 检查Java是否安装
if ! command -v java &> /dev/null; then
    echo "Error: Java is not installed or not in PATH"
    exit 1
fi

# 检查Maven是否安装
if ! command -v mvn &> /dev/null; then
    echo "Error: <PERSON>ven is not installed or not in PATH"
    exit 1
fi

echo "Compiling project..."
mvn clean compile
if [ $? -ne 0 ]; then
    echo "Error: Compilation failed"
    exit 1
fi

echo "Starting server..."
mvn spring-boot:run
