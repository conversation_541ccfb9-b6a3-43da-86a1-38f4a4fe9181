@echo off
echo Compiling and running Simple Letta MCP Server...
echo.

set JAVA_HOME=D:\soft\jdk8
echo JAVA_HOME set to: %JAVA_HOME%

echo Creating output directory...
if not exist "out" mkdir out

echo Compiling Java files...
javac -d out -sourcepath src/main/java src/main/java/org/auth2/letta/simple/SimpleLettaMcpServer.java

if %errorlevel% neq 0 (
    echo Error: Compilation failed
    pause
    exit /b 1
)

echo Starting Simple Letta MCP Server...
java -cp out org.auth2.letta.simple.SimpleLettaMcpServer

pause
