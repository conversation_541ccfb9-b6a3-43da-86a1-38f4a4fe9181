# Letta MCP Server

这是一个基于Java Spring Boot的MCP (Model Context Protocol) Server，用于与Letta Agent进行交互。

## 功能特性

- 🚀 支持与Letta Agent的流式通信
- 🔧 完整的MCP协议实现
- 📡 RESTful API接口
- 🧪 内置测试接口
- ⚡ 异步和同步两种通信模式

## 快速开始

### 1. 环境要求

- Java 8+
- Maven 3.6+
- Letta Agent服务运行在 `http://localhost:8283`

### 2. 配置

编辑 `src/main/resources/application.yml` 文件：

```yaml
letta:
  agent:
    base-url: http://localhost:8283  # Letta Agent服务地址
    id: agent-491a30d3-f49a-4b24-bf07-9d78064ce7cd  # Agent ID
```

### 3. 运行

```bash
# 编译
mvn clean compile

# 运行
mvn spring-boot:run
```

服务将在 `http://localhost:8080` 启动。

## API接口

### MCP协议接口

#### 初始化
```bash
POST /mcp/initialize
Content-Type: application/json

{
  "protocolVersion": "2024-11-05",
  "capabilities": {},
  "clientInfo": {
    "name": "test-client",
    "version": "1.0.0"
  }
}
```

#### 列出工具
```bash
POST /mcp/tools/list
Content-Type: application/json

{}
```

#### 调用工具
```bash
POST /mcp/tools/call
Content-Type: application/json

{
  "params": {
    "name": "letta_chat",
    "arguments": {
      "message": "Hello, how are you?"
    }
  }
}
```

#### 流式聊天
```bash
POST /mcp/chat/stream
Content-Type: application/json
Accept: text/event-stream

{
  "message": "Hello, how are you?"
}
```

### 测试接口

#### 健康检查
```bash
GET /test/health
```

#### 同步聊天测试
```bash
POST /test/chat
Content-Type: application/json

{
  "message": "Hello, how are you?"
}
```

#### 流式聊天测试
```bash
POST /test/chat/stream
Content-Type: application/json
Accept: text/event-stream

{
  "message": "Hello, how are you?"
}
```

## 原始curl命令对应

原始的curl命令：
```bash
curl -X POST \
    -H 'Content-Type: application/json' \
    -H 'Accept: text/event-stream' \
    -d '{
  "messages": [
    {
      "role": "user",
      "content": ""
    }
  ],
  "stream_steps": true,
  "stream_tokens": true
}' \
    http://localhost:8283/v1/agents/agent-491a30d3-f49a-4b24-bf07-9d78064ce7cd/messages/stream
```

现在可以通过以下方式调用：
```bash
# 使用MCP工具调用
curl -X POST http://localhost:8080/mcp/tools/call \
  -H 'Content-Type: application/json' \
  -d '{
    "params": {
      "name": "letta_chat",
      "arguments": {
        "message": "你好"
      }
    }
  }'

# 或者直接使用流式接口
curl -X POST http://localhost:8080/mcp/chat/stream \
  -H 'Content-Type: application/json' \
  -H 'Accept: text/event-stream' \
  -d '{"message": "你好"}'
```

## IDE集成

这个MCP Server可以被各种支持MCP协议的IDE和工具使用，如：

- Claude Desktop
- VS Code (通过MCP扩展)
- 其他支持MCP的开发工具

配置示例：
```json
{
  "mcpServers": {
    "letta": {
      "command": "java",
      "args": ["-jar", "target/letta-0.0.1-SNAPSHOT.jar"],
      "env": {
        "LETTA_AGENT_BASE_URL": "http://localhost:8283",
        "LETTA_AGENT_ID": "agent-491a30d3-f49a-4b24-bf07-9d78064ce7cd"
      }
    }
  }
}
```

## 项目结构

```
src/main/java/org/auth2/letta/
├── LettaApplication.java          # 主应用类
├── controller/
│   ├── McpController.java         # MCP协议控制器
│   └── TestController.java        # 测试控制器
├── service/
│   └── LettaAgentService.java     # Letta Agent服务
└── model/
    ├── Message.java               # 消息模型
    └── ChatRequest.java           # 聊天请求模型
```

## 开发和调试

1. 确保Letta Agent服务正在运行
2. 启动MCP Server
3. 使用测试接口验证连接
4. 通过MCP协议接口进行集成测试

## 故障排除

1. **连接失败**: 检查Letta Agent服务是否运行在正确的地址和端口
2. **Agent ID错误**: 确认配置文件中的Agent ID是否正确
3. **流式响应问题**: 检查网络连接和防火墙设置
